const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function checkAdmin() {
  try {
    console.log('Checking AdminUser table...');
    
    const adminUsers = await prisma.adminUser.findMany();
    console.log('Found AdminUsers:', adminUsers.length);
    
    for (const user of adminUsers) {
      console.log(`- ${user.email} (${user.role}) - Active: ${user.isActive}`);
    }
    
    // Try to find specific admin
    const admin = await prisma.adminUser.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (admin) {
      console.log('\nAdmin user found:', {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        isActive: admin.isActive
      });
      
      // Test password
      const isValid = await bcrypt.compare('admin123', admin.password);
      console.log('Password valid:', isValid);
    } else {
      console.log('\nAdmin user NOT found!');
      
      // Create admin user
      console.log('Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      const newAdmin = await prisma.adminUser.create({
        data: {
          email: '<EMAIL>',
          name: 'NS Shop Admin',
          password: hashedPassword,
          role: 'ADMIN',
          isActive: true,
          permissions: ['ALL']
        }
      });
      
      console.log('Admin user created:', newAdmin.email);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdmin();
