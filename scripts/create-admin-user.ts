import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Ki<PERSON>m tra xem admin user đã tồn tại chưa
    const existingAdmin = await prisma.user.findUnique({
      where: { email: "<EMAIL>" },
    });

    if (existingAdmin) {
      console.log("Admin user already exists");
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash("admin123", 12);

    // Tạo admin user
    const adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Admin User",
        password: hashedPassword,
        role: "ADMIN",
      },
    });

    console.log("Admin user created successfully:", adminUser.email);

    // Tạo thêm một user thường để test
    const existingUser = await prisma.user.findUnique({
      where: { email: "<EMAIL>" },
    });

    if (!existingUser) {
      const userPassword = await bcrypt.hash("password123", 12);

      const regularUser = await prisma.user.create({
        data: {
          email: "<EMAIL>",
          name: "Regular User",
          password: userPassword,
          role: "USER",
        },
      });

      console.log("Regular user created successfully:", regularUser.email);
    } else {
      console.log("Regular user already exists");
    }
  } catch (error) {
    console.error("Error creating admin user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
