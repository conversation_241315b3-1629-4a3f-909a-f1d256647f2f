import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function testAdminAPI() {
  try {
    console.log("🔍 Testing admin products API...");

    // Test direct database query
    console.log("\n1. Testing direct database query...");
    const products = await prisma.product.findMany({
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      take: 5,
    });

    console.log(`✅ Found ${products.length} products in database`);
    products.forEach((product, index) => {
      console.log(
        `  ${index + 1}. ${product.name} (${product.sku}) - ${product.category?.name || "No category"}`
      );
    });

    // Test pagination helper functions
    console.log("\n2. Testing pagination functions...");

    // Simulate request URL
    const testUrl = "http://localhost:3000/api/admin/products?page=1&limit=20";
    const mockRequest = {
      url: testUrl,
    } as any;

    // Import pagination helper
    const { PaginationHelper } = await import(
      "../src/lib/admin/api/pagination"
    );

    const pagination = PaginationHelper.extract(mockRequest);
    console.log("✅ Pagination params:", pagination);

    const search = PaginationHelper.extractSearch(mockRequest);
    console.log("✅ Search params:", search);

    const where = PaginationHelper.buildWhere(search, [
      "name",
      "description",
      "sku",
    ]);
    console.log("✅ Where clause:", where);

    const orderBy = PaginationHelper.buildOrderBy(search);
    console.log("✅ Order by:", orderBy);

    // Test the actual query that would be used
    console.log("\n3. Testing actual query...");
    const [items, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
        orderBy,
        skip: pagination.skip,
        take: pagination.limit,
      }),
      prisma.product.count({ where }),
    ]);

    console.log(`✅ Query successful: ${items.length} items, ${total} total`);

    // Test ProductController directly
    console.log("\n4. Testing ProductController...");
    const { ProductController } = await import(
      "../src/lib/admin/controllers/ProductController"
    );
    const controller = new ProductController();

    try {
      const result = await controller.list({ ...pagination, ...search });
      console.log("✅ ProductController.list() successful");
      console.log("Result structure:", {
        success: result.success,
        dataLength: result.data?.length || 0,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error("❌ ProductController.list() failed:", error);
    }

    // Test admin middleware
    console.log("\n5. Testing admin middleware...");
    try {
      const { withAdminAuth } = await import("../src/lib/admin/api/middleware");
      console.log("✅ Admin middleware imported successfully");
    } catch (error) {
      console.error("❌ Admin middleware import failed:", error);
    }
  } catch (error) {
    console.error("❌ Test failed:", error);
    console.error("Error details:", {
      name: error.name,
      message: error.message,
      stack: error.stack?.split("\n").slice(0, 5).join("\n"),
    });
  } finally {
    await prisma.$disconnect();
  }
}

testAdminAPI();
