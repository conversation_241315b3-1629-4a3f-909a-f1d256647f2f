import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function updateAdminRole() {
  try {
    // Find admin user by email
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (adminUser) {
      // Update role to ADMIN
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { role: 'ADMIN' }
      });
      console.log('✅ Updated <EMAIL> role to ADMIN');
    } else {
      // Create admin user if not exists
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Admin User',
          role: 'ADMIN',
          emailVerified: new Date(),
        }
      });
      console.log('✅ Created <EMAIL> with ADMIN role');
    }

    // Also ensure we have some test categories for testing
    const existingCategories = await prisma.category.findMany();
    
    if (existingCategories.length === 0) {
      await prisma.category.createMany({
        data: [
          {
            name: 'Thời trang',
            slug: 'thoi-trang',
            description: 'Danh mục thời trang và phụ kiện',
            image: 'https://via.placeholder.com/300x200'
          },
          {
            name: 'Thời trang nam',
            slug: 'thoi-trang-nam',
            description: 'Quần áo và phụ kiện dành cho nam giới',
            image: 'https://via.placeholder.com/300x200'
          },
          {
            name: 'Thời trang nữ',
            slug: 'thoi-trang-nu',
            description: 'Quần áo và phụ kiện dành cho nữ giới',
            image: 'https://via.placeholder.com/300x200'
          }
        ]
      });
      console.log('✅ Created test categories');
    }

    console.log('✅ Admin setup completed successfully');
  } catch (error) {
    console.error('❌ Error updating admin role:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAdminRole();
