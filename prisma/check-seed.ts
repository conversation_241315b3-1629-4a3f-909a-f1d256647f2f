import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function checkSeededData() {
  console.log("🔍 Checking seeded data...");

  try {
    // Count all data
    const adminCount = await prisma.adminUser.count();
    const userCount = await prisma.user.count();
    const categoryCount = await prisma.category.count();
    const productCount = await prisma.product.count();
    const orderCount = await prisma.order.count();
    const postCount = await prisma.post.count();
    const settingCount = await prisma.setting.count();

    console.log("\n📊 Current database state:");
    console.log(`✅ Admin accounts: ${adminCount}`);
    console.log(`✅ User accounts: ${userCount}`);
    console.log(`✅ Categories: ${categoryCount}`);
    console.log(`✅ Products: ${productCount}`);
    console.log(`✅ Orders (Bills): ${orderCount}`);
    console.log(`✅ Posts: ${postCount}`);
    console.log(`✅ Settings: ${settingCount}`);

    // Check specific requirements
    console.log("\n📋 Requirements check:");
    console.log(
      `Admin accounts: ${adminCount >= 1 ? "✅" : "❌"} (required: 1+)`
    );
    console.log(
      `User accounts: ${userCount >= 10 ? "✅" : "❌"} (required: 10)`
    );
    console.log(`Products: ${productCount >= 50 ? "✅" : "❌"} (required: 50)`);
    console.log(
      `Categories: ${categoryCount > 0 ? "✅" : "❌"} (required: some)`
    );
    console.log(`Orders: ${orderCount >= 20 ? "✅" : "❌"} (required: 20)`);
    console.log(`Posts: ${postCount >= 20 ? "✅" : "❌"} (required: 20)`);
    console.log(`Settings: ${settingCount > 0 ? "✅" : "❌"} (required: some)`);

    const allRequirementsMet =
      adminCount >= 1 &&
      userCount >= 10 &&
      productCount >= 50 &&
      categoryCount > 0 &&
      orderCount >= 20 &&
      postCount >= 20 &&
      settingCount > 0;

    console.log(
      `\n${allRequirementsMet ? "🎉" : "⚠️ "} Overall status: ${allRequirementsMet ? "ALL REQUIREMENTS MET!" : "Some requirements missing"}`
    );
  } catch (error) {
    console.error("❌ Error checking data:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSeededData();
