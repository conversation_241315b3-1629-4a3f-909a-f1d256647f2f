import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function seedBasicData() {
  console.log("🌱 Seeding basic data...");

  // Tạo admin user
  const adminPassword = await bcrypt.hash("admin123", 12);
  const admin = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "NS Shop Admin",
      password: adminPassword,
      role: "ADMIN",
      isActive: true,
    },
  });

  // Tạo moderator user
  const moderatorPassword = await bcrypt.hash("moderator123", 12);
  const moderator = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "NS Shop Moderator",
      password: moderatorPassword,
      role: "MODERATOR",
      isActive: true,
      permissions: {
        manage_products: true,
        manage_orders: true,
        manage_categories: true,
        view_analytics: true,
        manage_users: true,
      },
      createdBy: admin.id,
    },
  });

  return { admin, moderator };
}

async function seedUsers() {
  console.log("🌱 Seeding users...");

  const users = [];

  // Tạo 10 user accounts
  for (let i = 1; i <= 10; i++) {
    const userPassword = await bcrypt.hash(`user${i}123`, 12);
    const user = await prisma.user.upsert({
      where: { email: `user${i}@nsshop.com` },
      update: {},
      create: {
        email: `user${i}@nsshop.com`,
        name: `Người dùng ${i}`,
        password: userPassword,
        phone: `*********${i}`,
        gender: i % 2 === 0 ? "FEMALE" : "MALE",
      },
    });
    users.push(user);
  }

  return users;
}

async function seedSettings() {
  console.log("🌱 Seeding settings...");

  const settings = [
    { key: "siteName", value: "NS Shop", type: "string" },
    {
      key: "siteDescription",
      value: "Cửa hàng thời trang trực tuyến",
      type: "string",
    },
    { key: "contactEmail", value: "<EMAIL>", type: "string" },
    { key: "contactPhone", value: "**********", type: "string" },
    { key: "address", value: "123 Đường ABC, Quận 1, TP.HCM", type: "string" },
    { key: "currency", value: "VND", type: "string" },
    { key: "shippingFee", value: "30000", type: "number" },
    { key: "freeShippingThreshold", value: "500000", type: "number" },
  ];

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    });
  }
}

async function seedCategories() {
  console.log("🌱 Seeding categories...");

  const categories = [
    {
      name: "Thời trang nữ",
      slug: "thoi-trang-nu",
      description: "Quần áo, phụ kiện thời trang dành cho nữ",
      children: [
        { name: "Áo", slug: "ao-nu" },
        { name: "Quần", slug: "quan-nu" },
        { name: "Váy", slug: "vay" },
        { name: "Đầm", slug: "dam" },
      ],
    },
    {
      name: "Thời trang nam",
      slug: "thoi-trang-nam",
      description: "Quần áo, phụ kiện thời trang dành cho nam",
      children: [
        { name: "Áo", slug: "ao-nam" },
        { name: "Quần", slug: "quan-nam" },
      ],
    },
    {
      name: "Giày dép",
      slug: "giay-dep",
      description: "Giày, dép thời trang",
      children: [
        { name: "Giày nữ", slug: "giay-nu" },
        { name: "Giày nam", slug: "giay-nam" },
      ],
    },
  ];

  for (const category of categories) {
    const parentCategory = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: {
        name: category.name,
        slug: category.slug,
        description: category.description,
      },
    });

    // Tạo subcategories
    for (const child of category.children) {
      await prisma.category.upsert({
        where: { slug: child.slug },
        update: {},
        create: {
          name: child.name,
          slug: child.slug,
          parentId: parentCategory.id,
        },
      });
    }
  }
}

async function seedSampleProducts() {
  console.log("🌱 Seeding 50 sample products...");

  // Lấy categories
  const categories = await prisma.category.findMany({
    where: { parentId: { not: null } }, // Chỉ lấy subcategories
  });

  if (categories.length === 0) {
    throw new Error("Categories not found. Please run seedCategories first.");
  }

  const productTemplates = [
    {
      names: ["Áo Blouse", "Áo Sơ Mi", "Áo Thun", "Áo Khoác", "Áo Len"],
      descriptions: [
        "Chất liệu voan mềm mại, thiết kế sang trọng",
        "Cotton cao cấp, thoáng mát",
        "Form dáng ôm, tôn dáng",
        "Phong cách trẻ trung, năng động",
        "Ấm áp, phù hợp mùa đông",
      ],
      priceRange: [200000, 800000],
    },
    {
      names: ["Quần Jeans", "Quần Tây", "Quần Short", "Chân Váy", "Đầm Maxi"],
      descriptions: [
        "Chất liệu jeans co giãn thoải mái",
        "Thiết kế công sở chuyên nghiệp",
        "Phù hợp hoạt động thể thao",
        "Điệu đà, nữ tính",
        "Dáng dài sang trọng",
      ],
      priceRange: [300000, 1200000],
    },
    {
      names: ["Giày Sneaker", "Giày Cao Gót", "Giày Boot", "Sandal", "Dép"],
      descriptions: [
        "Đế êm, phù hợp đi bộ",
        "Thiết kế thanh lịch",
        "Phong cách cá tính",
        "Thoáng khí, thoải mái",
        "Chất liệu cao su bền bỉ",
      ],
      priceRange: [400000, 2000000],
    },
  ];

  const styles = ["Vintage", "Modern", "Classic", "Trendy", "Minimalist"];
  const colors = ["Đen", "Trắng", "Xám", "Hồng", "Xanh", "Đỏ", "Vàng", "Nâu"];

  for (let i = 1; i <= 50; i++) {
    const template = productTemplates[i % productTemplates.length];
    const baseName = template.names[i % template.names.length];
    const style = styles[i % styles.length];
    const color = colors[i % colors.length];
    const description = template.descriptions[i % template.descriptions.length];

    const name = `${baseName} ${style} ${color}`;
    const slug = name
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
      .replace(/[èéẹẻẽêềếệểễ]/g, "e")
      .replace(/[ìíịỉĩ]/g, "i")
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
      .replace(/[ùúụủũưừứựửữ]/g, "u")
      .replace(/[ỳýỵỷỹ]/g, "y")
      .replace(/[đ]/g, "d")
      .replace(/[^a-z0-9-]/g, "");

    const price = Math.floor(
      Math.random() * (template.priceRange[1] - template.priceRange[0]) +
        template.priceRange[0]
    );
    const salePrice = Math.random() > 0.6 ? Math.floor(price * 0.8) : null;
    const stock = Math.floor(Math.random() * 100) + 10;
    const categoryId = categories[i % categories.length].id;

    await prisma.product.upsert({
      where: { slug: slug },
      update: {},
      create: {
        name,
        slug,
        description: `${description}. ${name} với thiết kế hiện đại, chất lượng cao.`,
        price,
        salePrice,
        sku: `SP${String(i).padStart(3, "0")}`,
        stock,
        categoryId,
        images: [
          `https://images.unsplash.com/photo-${************* + i}?w=500`,
        ],
        featured: i <= 10, // 10 sản phẩm đầu là featured
        tags: [style.toLowerCase(), color.toLowerCase()],
      },
    });
  }
}

async function seedOrders(users: any[]) {
  console.log("🌱 Seeding 20 orders (bills)...");

  const products = await prisma.product.findMany({
    take: 20,
  });

  const statuses = [
    "PENDING",
    "CONFIRMED",
    "PROCESSING",
    "SHIPPED",
    "DELIVERED",
  ];
  const paymentMethods = ["COD", "BANK_TRANSFER"];
  const paymentStatuses = ["PENDING", "PAID"];

  for (let i = 1; i <= 20; i++) {
    const user = users[i % users.length];
    const orderProducts = products.slice(0, Math.floor(Math.random() * 3) + 1); // 1-3 sản phẩm

    let totalAmount = 0;
    const orderItems = orderProducts.map((product) => {
      const quantity = Math.floor(Math.random() * 3) + 1;
      const price = product.salePrice || product.price;
      const total = price * quantity;
      totalAmount += total;

      return {
        productId: product.id,
        quantity,
        price,
        total,
      };
    });

    const shippingFee = totalAmount >= 500000 ? 0 : 30000;
    const finalTotal = totalAmount + shippingFee;

    await prisma.order.create({
      data: {
        userId: user.id,
        status: statuses[i % statuses.length] as any,
        total: finalTotal,
        paymentMethod: paymentMethods[i % paymentMethods.length] as any,
        paymentStatus: paymentStatuses[i % paymentStatuses.length] as any,
        shippingAddress: {
          fullName: user.name,
          phone: user.phone,
          address: `Địa chỉ ${i}, Phường ${i}, Quận ${(i % 12) + 1}`,
          city: "TP. Hồ Chí Minh",
          country: "Việt Nam",
        },
        items: {
          create: orderItems,
        },
      },
    });
  }
}

async function seedPosts(admins: any[]) {
  console.log("🌱 Seeding 20 posts...");

  const categories = await prisma.category.findMany({
    where: { parentId: null }, // Lấy parent categories
  });

  const postTitles = [
    "Xu hướng thời trang mùa hè 2024",
    "Cách phối đồ công sở thanh lịch",
    "10 mẫu áo sơ mi hot nhất hiện tại",
    "Giày sneaker - Xu hướng không bao giờ lỗi thời",
    "Bí quyết chọn quần jeans phù hợp với từng dáng người",
    "Thời trang vintage đang trở lại mạnh mẽ",
    "Cách chăm sóc quần áo để bền đẹp",
    "Xu hướng màu sắc thời trang năm nay",
    "Phụ kiện không thể thiếu trong tủ đồ",
    "Cách mix đồ đi chơi cuối tuần",
    "Thời trang bền vững - Xu hướng tương lai",
    "10 items cơ bản mọi cô gái nên có",
    "Cách chọn size quần áo chuẩn nhất",
    "Xu hướng áo khoác mùa đông",
    "Phong cách minimalist trong thời trang",
    "Cách phối màu trong trang phục",
    "Thời trang cho dân văn phòng",
    "Giày cao gót - Làm sao để đi thoải mái",
    "Xu hướng túi xách hot trend",
    "Bí quyết mua sắm thông minh",
  ];

  const contentTemplate = `
<h2>Giới thiệu</h2>
<p>Trong thế giới thời trang không ngừng thay đổi, việc nắm bắt được những xu hướng mới nhất là điều vô cùng quan trọng. Bài viết này sẽ chia sẻ với bạn những thông tin hữu ích và cập nhật nhất.</p>

<h3>Nội dung chính</h3>
<p>Chúng ta sẽ cùng khám phá những điều thú vị và bổ ích trong lĩnh vực thời trang. Từ những xu hướng mới nhất đến các bí quyết phối đồ, tất cả đều sẽ được trình bày một cách chi tiết và dễ hiểu.</p>

<h3>Lời khuyên hữu ích</h3>
<ul>
<li>Luôn chọn những món đồ phù hợp với vóc dáng của bạn</li>
<li>Đầu tư vào những items cơ bản, chất lượng tốt</li>
<li>Học cách phối màu harmonious</li>
<li>Đừng quên chú ý đến phụ kiện</li>
</ul>

<h3>Kết luận</h3>
<p>Thời trang không chỉ là cách chúng ta ăn mặc mà còn là cách thể hiện cá tính và phong cách riêng. Hãy luôn tự tin và sáng tạo trong cách phối đồ của mình!</p>
  `;

  for (let i = 1; i <= 20; i++) {
    const title = postTitles[i - 1];
    const slug = title
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
      .replace(/[èéẹẻẽêềếệểễ]/g, "e")
      .replace(/[ìíịỉĩ]/g, "i")
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
      .replace(/[ùúụủũưừứựửữ]/g, "u")
      .replace(/[ỳýỵỷỹ]/g, "y")
      .replace(/[đ]/g, "d")
      .replace(/[^a-z0-9-]/g, "");

    const author = admins[i % admins.length];
    const category = categories[i % categories.length];

    await prisma.post.upsert({
      where: { slug },
      update: {},
      create: {
        title,
        slug,
        content: contentTemplate,
        excerpt: `${title} - Khám phá những thông tin thú vị và hữu ích trong thế giới thời trang hiện đại.`,
        status: i <= 15 ? "PUBLISHED" : "DRAFT", // 15 bài published, 5 bài draft
        featured: i <= 5, // 5 bài featured
        featuredImage: `https://images.unsplash.com/photo-${1600000000000 + i}?w=800`,
        tags: ["thời trang", "xu hướng", "phong cách"],
        categoryId: category.id,
        authorId: author.id,
        viewCount: Math.floor(Math.random() * 1000),
      },
    });
  }
}

async function main() {
  console.log("🚀 Starting NS Shop database seeding...");

  try {
    const { admin, moderator } = await seedBasicData();
    const users = await seedUsers();
    await seedSettings();
    await seedCategories();
    await seedSampleProducts();
    await seedOrders(users);
    await seedPosts([admin, moderator]);

    console.log("✅ Database seeding completed successfully!");
    console.log("\n📧 Login credentials:");
    console.log("Admin: <EMAIL> / admin123");
    console.log("Moderator: <EMAIL> / moderator123");
    console.log("Users: <EMAIL> / user1123 (user1-user10)");
    console.log("\n📊 Seeded data summary:");
    console.log("- 2 admin accounts (1 admin, 1 moderator)");
    console.log("- 10 user accounts");
    console.log("- 50 products");
    console.log("- Categories with subcategories");
    console.log("- 20 orders (bills)");
    console.log("- 20 posts");
    console.log("- Default settings");
  } catch (error) {
    console.error("❌ Database seeding failed:", error);
    process.exit(1);
  }
}

main()
  .catch((e) => {
    console.error("❌ Seed error:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
