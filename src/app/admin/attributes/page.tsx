"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Plus,
  Search,
  Settings,
  Edit,
  Trash2,
  Package,
  Filter,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Attribute,
  AttributeType,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";

export default function AttributesPage() {
  const [attributes, setAttributes] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: "",
    type: "",
    isRequired: "",
    isFilterable: "",
    page: 1,
    limit: 20,
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });

  // Fetch attributes
  const fetchAttributes = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });

      const response = await fetch(`/api/admin/attributes?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAttributes(data.data || []);
        setPagination(data.pagination || {});
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách thuộc tính");
      }
    } catch (error) {
      console.error("Error fetching attributes:", error);
      toast.error("Có lỗi xảy ra khi tải danh sách thuộc tính");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAttributes();
  }, [filters]);

  const handleSearch = () => {
    setFilters((prev) => ({ ...prev, page: 1 }));
  };

  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa thuộc tính "${name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Xóa thuộc tính thành công");
        fetchAttributes();
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi xóa thuộc tính");
      }
    } catch (error) {
      console.error("Error deleting attribute:", error);
      toast.error("Có lỗi xảy ra khi xóa thuộc tính");
    }
  };

  const getTypeColor = (type: AttributeType) => {
    const colors = {
      TEXT: "bg-blue-100 text-blue-800",
      NUMBER: "bg-green-100 text-green-800",
      COLOR: "bg-purple-100 text-purple-800",
      SIZE: "bg-orange-100 text-orange-800",
      BOOLEAN: "bg-gray-100 text-gray-800",
      SELECT: "bg-yellow-100 text-yellow-800",
      MULTI_SELECT: "bg-pink-100 text-pink-800",
    };
    return colors[type] || "bg-gray-100 text-gray-800";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quản lý thuộc tính</h1>
          <p className="text-muted-foreground">
            Quản lý thuộc tính sản phẩm như màu sắc, kích thước, chất liệu
          </p>
        </div>
        <Link href="/admin/attributes/create">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Thêm thuộc tính
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Bộ lọc
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm thuộc tính..."
                  value={filters.search}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, search: e.target.value }))
                  }
                  className="pl-10"
                />
              </div>
            </div>
            <Select
              value={filters.type}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, type: value }))
              }
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Loại thuộc tính" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả</SelectItem>
                {Object.entries(ATTRIBUTE_TYPE_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={filters.isRequired}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, isRequired: value }))
              }
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Bắt buộc" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả</SelectItem>
                <SelectItem value="true">Bắt buộc</SelectItem>
                <SelectItem value="false">Không bắt buộc</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Attributes Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Danh sách thuộc tính ({pagination.total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Đang tải...</p>
            </div>
          ) : attributes.length === 0 ? (
            <div className="text-center py-8">
              <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                Chưa có thuộc tính nào
              </h3>
              <p className="text-muted-foreground mb-4">
                Bắt đầu bằng cách thêm thuộc tính đầu tiên cho sản phẩm
              </p>
              <Link href="/admin/attributes/create">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm thuộc tính
                </Button>
              </Link>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Thuộc tính</TableHead>
                    <TableHead>Loại</TableHead>
                    <TableHead>Giá trị</TableHead>
                    <TableHead>Sản phẩm</TableHead>
                    <TableHead>Trạng thái</TableHead>
                    <TableHead>Thao tác</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {attributes.map((attribute) => (
                    <TableRow key={attribute.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{attribute.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {attribute.slug}
                          </div>
                          {attribute.description && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {attribute.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getTypeColor(attribute.type)}>
                          {ATTRIBUTE_TYPE_LABELS[attribute.type]}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Package className="h-4 w-4 mr-1" />
                          {attribute._count?.values || 0}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Package className="h-4 w-4 mr-1" />
                          {attribute._count?.products || 0}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {attribute.isRequired && (
                            <Badge variant="destructive" className="text-xs">
                              Bắt buộc
                            </Badge>
                          )}
                          {attribute.isFilterable && (
                            <Badge variant="secondary" className="text-xs">
                              Có thể lọc
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Link href={`/admin/attributes/${attribute.id}/edit`}>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleDelete(attribute.id, attribute.name)
                            }
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <p className="text-sm text-muted-foreground">
                    Hiển thị {(pagination.page - 1) * pagination.limit + 1} đến{" "}
                    {Math.min(
                      pagination.page * pagination.limit,
                      pagination.total
                    )}{" "}
                    trong tổng số {pagination.total} thuộc tính
                  </p>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))
                      }
                      disabled={pagination.page <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Trước
                    </Button>
                    <span className="text-sm">
                      Trang {pagination.page} / {pagination.pages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))
                      }
                      disabled={pagination.page >= pagination.pages}
                    >
                      Sau
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
