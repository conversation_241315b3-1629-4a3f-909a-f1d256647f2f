"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Settings,
  AlertCircle,
  Info
} from "lucide-react";
import { toast } from "sonner";

// Placeholder data structure for attributes
interface Attribute {
  id: string;
  name: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean';
  required: boolean;
  options?: string[];
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export default function AdminAttributesPage() {
  const [attributes, setAttributes] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    // Since attributes feature is not implemented yet, show placeholder data
    setTimeout(() => {
      setAttributes([
        {
          id: "1",
          name: "<PERSON><PERSON><PERSON> sắ<PERSON>",
          type: "select",
          required: false,
          options: ["Đỏ", "<PERSON>an<PERSON>", "Vàng", "Đen", "Trắng"],
          description: "Màu sắc của sản phẩm",
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-15T10:00:00Z"
        },
        {
          id: "2", 
          name: "Kích thước",
          type: "select",
          required: true,
          options: ["S", "M", "L", "XL", "XXL"],
          description: "Kích thước sản phẩm",
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-15T10:00:00Z"
        },
        {
          id: "3",
          name: "Chất liệu",
          type: "text",
          required: false,
          description: "Chất liệu sản phẩm",
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-15T10:00:00Z"
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'text': return 'bg-blue-100 text-blue-800';
      case 'number': return 'bg-green-100 text-green-800';
      case 'select': return 'bg-purple-100 text-purple-800';
      case 'multiselect': return 'bg-orange-100 text-orange-800';
      case 'boolean': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'text': return 'Văn bản';
      case 'number': return 'Số';
      case 'select': return 'Lựa chọn';
      case 'multiselect': return 'Đa lựa chọn';
      case 'boolean': return 'Đúng/Sai';
      default: return type;
    }
  };

  const filteredAttributes = attributes.filter(attr =>
    attr.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    attr.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateAttribute = () => {
    toast.info("Tính năng này đang được phát triển");
  };

  const handleEditAttribute = (id: string) => {
    toast.info("Tính năng này đang được phát triển");
  };

  const handleDeleteAttribute = (id: string) => {
    toast.info("Tính năng này đang được phát triển");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý thuộc tính</h1>
          <p className="text-muted-foreground">
            Quản lý các thuộc tính sản phẩm như màu sắc, kích thước, chất liệu
          </p>
        </div>
        <Button onClick={handleCreateAttribute}>
          <Plus className="h-4 w-4 mr-2" />
          Thêm thuộc tính
        </Button>
      </div>

      {/* Development Notice */}
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Info className="h-5 w-5 text-orange-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-orange-800">Tính năng đang phát triển</h3>
              <p className="text-sm text-orange-700 mt-1">
                Trang quản lý thuộc tính sản phẩm hiện đang trong giai đoạn phát triển. 
                Dữ liệu hiển thị dưới đây chỉ mang tính chất demo.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Tìm kiếm thuộc tính..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Attributes List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAttributes.map((attribute) => (
          <Card key={attribute.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{attribute.name}</CardTitle>
                <div className="flex items-center space-x-2">
                  <Badge className={getTypeColor(attribute.type)}>
                    {getTypeLabel(attribute.type)}
                  </Badge>
                  {attribute.required && (
                    <Badge variant="destructive" className="text-xs">
                      Bắt buộc
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {attribute.description && (
                <p className="text-sm text-muted-foreground">
                  {attribute.description}
                </p>
              )}
              
              {attribute.options && attribute.options.length > 0 && (
                <div>
                  <p className="text-sm font-medium mb-2">Tùy chọn:</p>
                  <div className="flex flex-wrap gap-1">
                    {attribute.options.slice(0, 3).map((option, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {option}
                      </Badge>
                    ))}
                    {attribute.options.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{attribute.options.length - 3} khác
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-xs text-muted-foreground">
                  Tạo: {new Date(attribute.createdAt).toLocaleDateString('vi-VN')}
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditAttribute(attribute.id)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteAttribute(attribute.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAttributes.length === 0 && (
        <div className="text-center py-12">
          <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Không tìm thấy thuộc tính</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm ? "Không có thuộc tính nào khớp với từ khóa tìm kiếm" : "Chưa có thuộc tính nào được tạo"}
          </p>
          <Button onClick={handleCreateAttribute}>
            <Plus className="h-4 w-4 mr-2" />
            Thêm thuộc tính đầu tiên
          </Button>
        </div>
      )}
    </div>
  );
}
