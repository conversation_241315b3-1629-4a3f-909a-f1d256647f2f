import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { AdminSession, ApiResponse } from '../types';

/**
 * Admin authentication middleware
 * Checks if user is authenticated and has admin role
 */
export async function withAdminAuth(
  request: NextRequest,
  handler: (request: NextRequest, session: AdminSession) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: 'Chưa đăng nhập', success: false },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Không có quyền truy cập', success: false },
        { status: 403 }
      );
    }

    return await handler(request, session as AdminSession);
  } catch (error) {
    console.error('Admin auth middleware error:', error);
    return NextResponse.json(
      { error: 'Lỗi xác thực', success: false },
      { status: 500 }
    );
  }
}

/**
 * Permission-based middleware
 * Checks if user has specific permissions
 */
export async function withPermissions(
  request: NextRequest,
  permissions: string[],
  handler: (request: NextRequest, session: AdminSession) => Promise<NextResponse>
): Promise<NextResponse> {
  return withAdminAuth(request, async (req, session) => {
    // For now, all admins have all permissions
    // In the future, implement role-based permissions
    return await handler(req, session);
  });
}

/**
 * Rate limiting middleware
 * Limits requests per IP address
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export async function withRateLimit(
  request: NextRequest,
  limit: number = 100,
  windowMs: number = 15 * 60 * 1000, // 15 minutes
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const now = Date.now();
  
  const current = rateLimitMap.get(ip);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    return await handler(request);
  }
  
  if (current.count >= limit) {
    return NextResponse.json(
      { error: 'Quá nhiều yêu cầu, vui lòng thử lại sau', success: false },
      { status: 429 }
    );
  }
  
  current.count++;
  return await handler(request);
}

/**
 * CORS middleware for admin API
 */
export async function withCors(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  const response = await handler(request);
  
  // Add CORS headers
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  return response;
}

/**
 * Request validation middleware
 */
export async function withValidation<T>(
  request: NextRequest,
  schema: any, // Zod schema
  handler: (request: NextRequest, data: T) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedData = schema.parse(body);
    return await handler(request, validatedData);
  } catch (error: any) {
    if (error.errors) {
      // Zod validation error
      const errorMessages = error.errors.map((err: any) => 
        `${err.path.join('.')}: ${err.message}`
      ).join(', ');
      
      return NextResponse.json(
        { error: `Dữ liệu không hợp lệ: ${errorMessages}`, success: false },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Dữ liệu không hợp lệ', success: false },
      { status: 400 }
    );
  }
}

/**
 * Logging middleware
 */
export async function withLogging(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  const start = Date.now();
  const method = request.method;
  const url = request.url;
  
  console.log(`[${new Date().toISOString()}] ${method} ${url} - Started`);
  
  try {
    const response = await handler(request);
    const duration = Date.now() - start;
    
    console.log(`[${new Date().toISOString()}] ${method} ${url} - ${response.status} (${duration}ms)`);
    
    return response;
  } catch (error) {
    const duration = Date.now() - start;
    console.error(`[${new Date().toISOString()}] ${method} ${url} - Error (${duration}ms):`, error);
    throw error;
  }
}

/**
 * Combine multiple middlewares
 */
export function combineMiddlewares(
  ...middlewares: Array<(
    request: NextRequest,
    handler: (request: NextRequest) => Promise<NextResponse>
  ) => Promise<NextResponse>>
) {
  return (
    request: NextRequest,
    finalHandler: (request: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    return middlewares.reduceRight(
      (handler, middleware) => (req: NextRequest) => middleware(req, handler),
      finalHandler
    )(request);
  };
}
