'use client';

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { ApiResponse, PaginatedResponse, PaginationParams, SearchParams } from '../types';

interface UseAdminDataOptions {
  endpoint: string;
  initialParams?: SearchParams & Partial<PaginationParams>;
  autoFetch?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

interface UseAdminDataReturn<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  pagination: PaginatedResponse<T>['pagination'] | null;
  refresh: () => Promise<void>;
  setParams: (params: Partial<SearchParams & PaginationParams>) => void;
  params: SearchParams & PaginationParams;
}

/**
 * Hook for fetching admin data with pagination and search
 */
export function useAdminData<T = any>(
  options: UseAdminDataOptions
): UseAdminDataReturn<T> {
  const {
    endpoint,
    initialParams = {},
    autoFetch = true,
    onSuccess,
    onError,
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginatedResponse<T>['pagination'] | null>(null);
  const [params, setParamsState] = useState<SearchParams & PaginationParams>({
    page: 1,
    limit: 20,
    ...initialParams,
  });

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });

      const response = await fetch(`${endpoint}?${searchParams.toString()}`);
      const result: PaginatedResponse<T> = await response.json();

      if (response.ok && result.success) {
        setData(result.data || []);
        setPagination(result.pagination || null);
        onSuccess?.(result.data);
      } else {
        const errorMessage = result.error || 'Có lỗi xảy ra khi tải dữ liệu';
        setError(errorMessage);
        onError?.(errorMessage);
        toast.error(errorMessage);
      }
    } catch (err) {
      const errorMessage = 'Có lỗi xảy ra khi tải dữ liệu';
      setError(errorMessage);
      onError?.(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [endpoint, params, onSuccess, onError]);

  const setParams = useCallback((newParams: Partial<SearchParams & PaginationParams>) => {
    setParamsState(prev => ({
      ...prev,
      ...newParams,
      // Reset to page 1 when search params change (except when changing page)
      ...(newParams.page === undefined && { page: 1 }),
    }));
  }, []);

  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [fetchData, autoFetch]);

  return {
    data,
    loading,
    error,
    pagination,
    refresh,
    setParams,
    params,
  };
}

/**
 * Hook for fetching single admin item
 */
export function useAdminItem<T = any>(endpoint: string, id?: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchItem = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${endpoint}/${id}`);
      const result: ApiResponse<T> = await response.json();

      if (response.ok && result.success) {
        setData(result.data || null);
      } else {
        const errorMessage = result.error || 'Có lỗi xảy ra khi tải dữ liệu';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (err) {
      const errorMessage = 'Có lỗi xảy ra khi tải dữ liệu';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [endpoint, id]);

  const refresh = useCallback(async () => {
    await fetchItem();
  }, [fetchItem]);

  useEffect(() => {
    fetchItem();
  }, [fetchItem]);

  return {
    data,
    loading,
    error,
    refresh,
  };
}

/**
 * Hook for admin CRUD operations
 */
export function useAdminCrud<T = any>(endpoint: string) {
  const [loading, setLoading] = useState(false);

  const create = useCallback(async (data: Partial<T>): Promise<T | null> => {
    setLoading(true);
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<T> = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Tạo thành công');
        return result.data || null;
      } else {
        toast.error(result.error || 'Có lỗi xảy ra khi tạo');
        return null;
      }
    } catch (err) {
      toast.error('Có lỗi xảy ra khi tạo');
      return null;
    } finally {
      setLoading(false);
    }
  }, [endpoint]);

  const update = useCallback(async (id: string, data: Partial<T>): Promise<T | null> => {
    setLoading(true);
    try {
      const response = await fetch(`${endpoint}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<T> = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Cập nhật thành công');
        return result.data || null;
      } else {
        toast.error(result.error || 'Có lỗi xảy ra khi cập nhật');
        return null;
      }
    } catch (err) {
      toast.error('Có lỗi xảy ra khi cập nhật');
      return null;
    } finally {
      setLoading(false);
    }
  }, [endpoint]);

  const remove = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`${endpoint}/${id}`, {
        method: 'DELETE',
      });

      const result: ApiResponse = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Xóa thành công');
        return true;
      } else {
        toast.error(result.error || 'Có lỗi xảy ra khi xóa');
        return false;
      }
    } catch (err) {
      toast.error('Có lỗi xảy ra khi xóa');
      return false;
    } finally {
      setLoading(false);
    }
  }, [endpoint]);

  const bulkDelete = useCallback(async (ids: string[]): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`${endpoint}/bulk`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });

      const result: ApiResponse = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Xóa hàng loạt thành công');
        return true;
      } else {
        toast.error(result.error || 'Có lỗi xảy ra khi xóa hàng loạt');
        return false;
      }
    } catch (err) {
      toast.error('Có lỗi xảy ra khi xóa hàng loạt');
      return false;
    } finally {
      setLoading(false);
    }
  }, [endpoint]);

  return {
    create,
    update,
    remove,
    bulkDelete,
    loading,
  };
}
