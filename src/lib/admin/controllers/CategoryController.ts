import { prisma } from '@/lib/prisma';
import { BaseAdminController } from '../api/base-controller';
import { ErrorHelper } from '../api/errors';
import { CategoryFormData } from '../types';

export class CategoryController extends BaseAdminController<CategoryFormData> {
  constructor() {
    super(
      prisma.category,
      'danh mục',
      ['name', 'description'], // searchable fields
      {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        children: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      }
    );
  }

  protected async validateCreateData(data: Partial<CategoryFormData>): Promise<void> {
    // Validate required fields
    if (!data.name || !data.slug) {
      throw ErrorHelper.validation('Thiếu thông tin bắt buộc');
    }

    // Check if slug already exists
    const existingSlug = await prisma.category.findUnique({
      where: { slug: data.slug },
    });

    if (existingSlug) {
      throw ErrorHelper.conflict('Slug đã tồn tại');
    }

    // Validate parent category exists if provided
    if (data.parentId) {
      const parentCategory = await prisma.category.findUnique({
        where: { id: data.parentId },
      });

      if (!parentCategory) {
        throw ErrorHelper.validation('Danh mục cha không tồn tại');
      }
    }
  }

  protected async validateUpdateData(id: string, data: Partial<CategoryFormData>): Promise<void> {
    // Check if slug already exists (excluding current category)
    if (data.slug) {
      const existingSlug = await prisma.category.findFirst({
        where: {
          slug: data.slug,
          NOT: { id },
        },
      });

      if (existingSlug) {
        throw ErrorHelper.conflict('Slug đã tồn tại');
      }
    }

    // Validate parent category exists if provided
    if (data.parentId) {
      // Check if parent exists
      const parentCategory = await prisma.category.findUnique({
        where: { id: data.parentId },
      });

      if (!parentCategory) {
        throw ErrorHelper.validation('Danh mục cha không tồn tại');
      }

      // Prevent circular reference (category cannot be its own parent or descendant)
      if (data.parentId === id) {
        throw ErrorHelper.validation('Danh mục không thể là cha của chính nó');
      }

      // Check if the new parent is a descendant of current category
      const isDescendant = await this.isDescendant(id, data.parentId);
      if (isDescendant) {
        throw ErrorHelper.validation('Không thể đặt danh mục con làm danh mục cha');
      }
    }
  }

  protected async transformCreateData(data: Partial<CategoryFormData>): Promise<any> {
    // Generate slug if not provided
    let slug = data.slug;
    if (!slug && data.name) {
      slug = data.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      // Ensure slug is unique
      let counter = 1;
      let finalSlug = slug;
      while (await prisma.category.findUnique({ where: { slug: finalSlug } })) {
        finalSlug = `${slug}-${counter}`;
        counter++;
      }
      slug = finalSlug;
    }

    return {
      ...data,
      slug,
      parentId: data.parentId || null,
    };
  }

  protected async transformUpdateData(data: Partial<CategoryFormData>): Promise<any> {
    const transformed: any = { ...data };

    // Handle parentId
    if (data.parentId !== undefined) {
      transformed.parentId = data.parentId || null;
    }

    return transformed;
  }

  protected async afterCreate(item: any): Promise<void> {
    console.log(`Category created: ${item.name} (ID: ${item.id})`);
  }

  protected async afterUpdate(item: any, oldItem: any): Promise<void> {
    console.log(`Category updated: ${item.name} (ID: ${item.id})`);
  }

  protected async afterDelete(item: any): Promise<void> {
    console.log(`Category deleted: ${item.name} (ID: ${item.id})`);
  }

  protected async validateDelete(id: string, item: any): Promise<void> {
    // Check if category has products
    const productCount = await prisma.product.count({
      where: { categoryId: id },
    });

    if (productCount > 0) {
      throw ErrorHelper.validation(
        `Không thể xóa danh mục có ${productCount} sản phẩm. Vui lòng di chuyển sản phẩm sang danh mục khác trước.`
      );
    }

    // Check if category has children
    const childrenCount = await prisma.category.count({
      where: { parentId: id },
    });

    if (childrenCount > 0) {
      throw ErrorHelper.validation(
        `Không thể xóa danh mục có ${childrenCount} danh mục con. Vui lòng xóa hoặc di chuyển danh mục con trước.`
      );
    }
  }

  protected supportsSoftDelete(): boolean {
    return false; // Categories are hard deleted
  }

  // Custom methods specific to categories
  private async isDescendant(categoryId: string, potentialParentId: string): Promise<boolean> {
    const descendants = await this.getDescendants(categoryId);
    return descendants.some(desc => desc.id === potentialParentId);
  }

  async getDescendants(categoryId: string): Promise<any[]> {
    const descendants: any[] = [];
    
    const children = await prisma.category.findMany({
      where: { parentId: categoryId },
    });

    for (const child of children) {
      descendants.push(child);
      const childDescendants = await this.getDescendants(child.id);
      descendants.push(...childDescendants);
    }

    return descendants;
  }

  async getCategoryTree(): Promise<any[]> {
    // Get all categories
    const allCategories = await prisma.category.findMany({
      include: this.includeRelations,
      orderBy: { name: 'asc' },
    });

    // Build tree structure
    const categoryMap = new Map();
    const rootCategories: any[] = [];

    // First pass: create map
    allCategories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Second pass: build tree
    allCategories.forEach(category => {
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      } else {
        rootCategories.push(categoryMap.get(category.id));
      }
    });

    return rootCategories;
  }

  async moveCategory(categoryId: string, newParentId?: string): Promise<any> {
    try {
      // Validate the move
      if (newParentId) {
        const newParent = await prisma.category.findUnique({
          where: { id: newParentId },
        });

        if (!newParent) {
          throw ErrorHelper.validation('Danh mục cha mới không tồn tại');
        }

        // Prevent circular reference
        if (newParentId === categoryId) {
          throw ErrorHelper.validation('Danh mục không thể là cha của chính nó');
        }

        const isDescendant = await this.isDescendant(categoryId, newParentId);
        if (isDescendant) {
          throw ErrorHelper.validation('Không thể di chuyển danh mục vào danh mục con của nó');
        }
      }

      // Update the category
      const updated = await prisma.category.update({
        where: { id: categoryId },
        data: { parentId: newParentId || null },
        include: this.includeRelations,
      });

      return updated;
    } catch (error) {
      throw ErrorHelper.database('Lỗi khi di chuyển danh mục', error);
    }
  }

  async getProductCount(categoryId: string): Promise<number> {
    try {
      return await prisma.product.count({
        where: { categoryId },
      });
    } catch (error) {
      throw ErrorHelper.database('Lỗi khi đếm sản phẩm trong danh mục', error);
    }
  }

  async getCategoriesWithProductCount(): Promise<any[]> {
    try {
      const categories = await prisma.category.findMany({
        include: {
          ...this.includeRelations,
          products: {
            select: { id: true },
          },
        },
        orderBy: [
          { parentId: 'asc' },
          { name: 'asc' },
        ],
      });

      return categories.map(category => ({
        ...category,
        productCount: category.products.length,
        products: undefined, // Remove products array to reduce payload
      }));
    } catch (error) {
      throw ErrorHelper.database('Lỗi khi lấy danh sách danh mục với số lượng sản phẩm', error);
    }
  }
}
