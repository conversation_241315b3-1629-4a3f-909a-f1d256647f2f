import { prisma } from '@/lib/prisma';
import { BaseAdminController } from '../api/base-controller';
import { ErrorHelper } from '../api/errors';
import { ProductFormData } from '../types';

export class ProductController extends BaseAdminController<ProductFormData> {
  constructor() {
    super(
      prisma.product,
      'sản phẩm',
      ['name', 'description', 'sku'], // searchable fields
      {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      }
    );
  }

  protected async validateCreateData(data: Partial<ProductFormData>): Promise<void> {
    // Validate required fields
    if (!data.name || !data.description || !data.categoryId || !data.price) {
      throw ErrorHelper.validation('Thiếu thông tin bắt buộc');
    }

    // Check if SKU already exists
    if (data.sku) {
      const existingSku = await prisma.product.findUnique({
        where: { sku: data.sku },
      });

      if (existingSku) {
        throw ErrorHelper.conflict('SKU đã tồn tại');
      }
    }

    // Validate category exists
    const category = await prisma.category.findUnique({
      where: { id: data.categoryId },
    });

    if (!category) {
      throw ErrorHelper.validation('Danh mục không tồn tại');
    }

    // Validate price
    if (data.price && data.price <= 0) {
      throw ErrorHelper.validation('Giá phải lớn hơn 0');
    }

    if (data.salePrice && data.salePrice <= 0) {
      throw ErrorHelper.validation('Giá khuyến mãi phải lớn hơn 0');
    }

    if (data.salePrice && data.price && data.salePrice >= data.price) {
      throw ErrorHelper.validation('Giá khuyến mãi phải nhỏ hơn giá gốc');
    }
  }

  protected async validateUpdateData(id: string, data: Partial<ProductFormData>): Promise<void> {
    // Check if SKU already exists (excluding current product)
    if (data.sku) {
      const existingSku = await prisma.product.findFirst({
        where: {
          sku: data.sku,
          NOT: { id },
        },
      });

      if (existingSku) {
        throw ErrorHelper.conflict('SKU đã tồn tại');
      }
    }

    // Validate category exists
    if (data.categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: data.categoryId },
      });

      if (!category) {
        throw ErrorHelper.validation('Danh mục không tồn tại');
      }
    }

    // Validate price
    if (data.price && data.price <= 0) {
      throw ErrorHelper.validation('Giá phải lớn hơn 0');
    }

    if (data.salePrice && data.salePrice <= 0) {
      throw ErrorHelper.validation('Giá khuyến mãi phải lớn hơn 0');
    }

    if (data.salePrice && data.price && data.salePrice >= data.price) {
      throw ErrorHelper.validation('Giá khuyến mãi phải nhỏ hơn giá gốc');
    }
  }

  protected async transformCreateData(data: Partial<ProductFormData>): Promise<any> {
    // Generate slug if not provided
    let slug = data.slug;
    if (!slug && data.name) {
      slug = data.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      // Ensure slug is unique
      let counter = 1;
      let finalSlug = slug;
      while (await prisma.product.findUnique({ where: { slug: finalSlug } })) {
        finalSlug = `${slug}-${counter}`;
        counter++;
      }
      slug = finalSlug;
    }

    // Generate SKU if not provided
    let sku = data.sku;
    if (!sku) {
      const timestamp = Date.now().toString().slice(-6);
      const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase();
      sku = `PRD-${timestamp}-${randomStr}`;

      // Ensure SKU is unique
      let counter = 1;
      let finalSku = sku;
      while (await prisma.product.findUnique({ where: { sku: finalSku } })) {
        finalSku = `${sku}-${counter}`;
        counter++;
      }
      sku = finalSku;
    }

    return {
      ...data,
      slug,
      sku,
      price: data.price ? parseFloat(String(data.price)) : undefined,
      salePrice: data.salePrice ? parseFloat(String(data.salePrice)) : null,
      stock: data.stock ? parseInt(String(data.stock)) : 0,
      featured: Boolean(data.featured),
      status: data.status || 'ACTIVE',
      images: data.images || [],
      tags: data.tags || [],
    };
  }

  protected async transformUpdateData(data: Partial<ProductFormData>): Promise<any> {
    const transformed: any = { ...data };

    // Parse numeric fields
    if (data.price !== undefined) {
      transformed.price = parseFloat(String(data.price));
    }

    if (data.salePrice !== undefined) {
      transformed.salePrice = data.salePrice ? parseFloat(String(data.salePrice)) : null;
    }

    if (data.stock !== undefined) {
      transformed.stock = parseInt(String(data.stock));
    }

    // Parse boolean fields
    if (data.featured !== undefined) {
      transformed.featured = Boolean(data.featured);
    }

    // Ensure arrays
    if (data.images !== undefined) {
      transformed.images = data.images || [];
    }

    if (data.tags !== undefined) {
      transformed.tags = data.tags || [];
    }

    return transformed;
  }

  protected async afterCreate(item: any): Promise<void> {
    // Log product creation
    console.log(`Product created: ${item.name} (ID: ${item.id})`);
    
    // You could add additional logic here like:
    // - Send notifications
    // - Update search index
    // - Clear cache
  }

  protected async afterUpdate(item: any, oldItem: any): Promise<void> {
    // Log product update
    console.log(`Product updated: ${item.name} (ID: ${item.id})`);
    
    // Check if stock changed and send notifications if needed
    if (oldItem.stock !== item.stock && item.stock <= 5) {
      console.log(`Low stock alert for product: ${item.name} (Stock: ${item.stock})`);
    }
  }

  protected async afterDelete(item: any): Promise<void> {
    // Log product deletion
    console.log(`Product deleted: ${item.name} (ID: ${item.id})`);
    
    // You could add additional logic here like:
    // - Remove from search index
    // - Clear cache
    // - Archive related data
  }

  protected supportsSoftDelete(): boolean {
    return false; // Products are hard deleted
  }

  // Custom methods specific to products
  async updateStock(id: string, stock: number) {
    try {
      const product = await this.model.update({
        where: { id },
        data: { stock },
        include: this.includeRelations,
      });

      return product;
    } catch (error) {
      throw ErrorHelper.database('Lỗi khi cập nhật tồn kho', error);
    }
  }

  async toggleFeatured(id: string) {
    try {
      const product = await this.model.findUnique({ where: { id } });
      if (!product) {
        throw ErrorHelper.notFound('Không tìm thấy sản phẩm');
      }

      const updated = await this.model.update({
        where: { id },
        data: { featured: !product.featured },
        include: this.includeRelations,
      });

      return updated;
    } catch (error) {
      throw ErrorHelper.database('Lỗi khi cập nhật trạng thái nổi bật', error);
    }
  }

  async updateStatus(id: string, status: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK') {
    try {
      const product = await this.model.update({
        where: { id },
        data: { status },
        include: this.includeRelations,
      });

      return product;
    } catch (error) {
      throw ErrorHelper.database('Lỗi khi cập nhật trạng thái sản phẩm', error);
    }
  }

  async getLowStockProducts(threshold: number = 5) {
    try {
      const products = await this.model.findMany({
        where: {
          stock: { lte: threshold },
          status: 'ACTIVE',
        },
        include: this.includeRelations,
        orderBy: { stock: 'asc' },
      });

      return products;
    } catch (error) {
      throw ErrorHelper.database('Lỗi khi lấy danh sách sản phẩm sắp hết hàng', error);
    }
  }
}
